2025-08-27 12:02:53,429 39116 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 12:02:56,270 39116 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2071, in statics
    for module in os.listdir(addons_path):
                  ^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo17\\server\\custom_arzu_live'
2025-08-27 12:02:56,270 39116 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:02:56] "GET /web/database/selector HTTP/1.1" 500 - 1 0.009 0.588
2025-08-27 12:02:56,398 39116 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2071, in statics
    for module in os.listdir(addons_path):
                  ^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo17\\server\\custom_arzu_live'
2025-08-27 12:02:56,398 39116 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:02:56] "GET /web/image/website/1/favicon?unique=183d671 HTTP/1.1" 500 - 1 0.000 0.100
2025-08-27 12:02:58,013 39116 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:02:58] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.007 0.093
