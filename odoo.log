2025-08-27 12:05:14,130 39664 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 12:05:14,130 39664 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 12:05:14,130 39664 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_addons', 'c:\\odoo17\\server\\custom_alfnia_testing', 'c:\\odoo17\\server\\custom_arzu_live'] 
2025-08-27 12:05:14,130 39664 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 12:05:14,230 39664 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 12:05:15,827 39664 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 12:05:16,630 39664 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2072, in statics
    manifest = get_manifest(module)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 369, in get_manifest
    return copy.deepcopy(_get_manifest_cached(module, mod_path))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 373, in _get_manifest_cached
    return load_manifest(module, mod_path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 319, in load_manifest
    manifest.update(ast.literal_eval(f.read()))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\_monkeypatches.py", line 78, in literal_eval
    return orig_literal_eval(expr)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 112, in literal_eval
    return _convert(node_or_string)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 101, in _convert
    return dict(zip(map(_convert, node.keys),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 111, in _convert
    return _convert_signed_num(node)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 85, in _convert_signed_num
    return _convert_num(node)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 76, in _convert_num
    _raise_malformed_node(node)
  File "C:\Odoo17\python\Lib\ast.py", line 73, in _raise_malformed_node
    raise ValueError(msg + f': {node!r}')
ValueError: malformed node or string on line 14: <ast.Name object at 0x000002113D929350>
2025-08-27 12:05:16,631 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:16] "GET /web/database/selector HTTP/1.1" 500 - 1 0.010 0.589
2025-08-27 12:05:16,764 39664 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2072, in statics
    manifest = get_manifest(module)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 369, in get_manifest
    return copy.deepcopy(_get_manifest_cached(module, mod_path))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 373, in _get_manifest_cached
    return load_manifest(module, mod_path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 319, in load_manifest
    manifest.update(ast.literal_eval(f.read()))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\_monkeypatches.py", line 78, in literal_eval
    return orig_literal_eval(expr)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 112, in literal_eval
    return _convert(node_or_string)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 101, in _convert
    return dict(zip(map(_convert, node.keys),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 111, in _convert
    return _convert_signed_num(node)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 85, in _convert_signed_num
    return _convert_num(node)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 76, in _convert_num
    _raise_malformed_node(node)
  File "C:\Odoo17\python\Lib\ast.py", line 73, in _raise_malformed_node
    raise ValueError(msg + f': {node!r}')
ValueError: malformed node or string on line 14: <ast.Name object at 0x000002113D96FB50>
2025-08-27 12:05:16,764 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:16] "GET /web/image/website/1/favicon?unique=183d671 HTTP/1.1" 500 - 1 0.000 0.113
2025-08-27 12:05:18,307 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:18] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.019 0.123
